com/healthconnect/entity/User.class
com/healthconnect/dto/ChatRequest.class
com/healthconnect/entity/AiMessage$AiMessageBuilder.class
com/healthconnect/entity/MessageType.class
com/healthconnect/repository/UserRepository.class
com/healthconnect/dto/UpdateProfileRequest.class
com/healthconnect/dto/ChatResponse.class
com/healthconnect/dto/AppointmentRequest.class
com/healthconnect/dto/MessageRequest.class
com/healthconnect/service/AppointmentService.class
com/healthconnect/entity/UserRole.class
com/healthconnect/entity/AiMessage.class
com/healthconnect/dto/SymptomAnalysisResponse$SymptomAnalysisResponseBuilder.class
com/healthconnect/controller/DoctorController.class
com/healthconnect/entity/SymptomAnalysis$UrgencyLevel.class
com/healthconnect/dto/UserResponse.class
com/healthconnect/controller/AppointmentController.class
com/healthconnect/dto/AiMessageResponse$AiMessageResponseBuilder.class
com/healthconnect/entity/AiConversation.class
com/healthconnect/dto/MessageResponse.class
com/healthconnect/repository/AiConversationRepository.class
com/healthconnect/entity/ChatStatus.class
com/healthconnect/HealthConnectApplication.class
com/healthconnect/service/AiHealthBotService.class
com/healthconnect/dto/AppointmentUpdateRequest.class
com/healthconnect/entity/AppointmentType.class
com/healthconnect/config/WebSocketConfig.class
com/healthconnect/service/AuthService.class
com/healthconnect/dto/AiChatResponse.class
com/healthconnect/dto/AiMessageResponse.class
com/healthconnect/dto/LoginRequest.class
com/healthconnect/entity/AvailabilityStatus.class
com/healthconnect/repository/AiMessageRepository.class
com/healthconnect/service/JwtService.class
com/healthconnect/dto/AuthResponse$AuthResponseBuilder.class
com/healthconnect/service/DoctorAvailabilityService.class
com/healthconnect/dto/RegisterRequest.class
com/healthconnect/dto/AppointmentChatRequest.class
com/healthconnect/controller/WebSocketController.class
com/healthconnect/dto/AuthResponse.class
com/healthconnect/entity/AiMessage$MessageRole.class
com/healthconnect/entity/DoctorAvailability.class
com/healthconnect/entity/AiConversation$ConversationType.class
com/healthconnect/dto/AiConversationResponse.class
com/healthconnect/dto/SymptomAnalysisResponse.class
com/healthconnect/dto/TimeSlotResponse.class
com/healthconnect/config/JwtAuthenticationFilter.class
com/healthconnect/controller/TestController.class
com/healthconnect/repository/ChatRepository.class
com/healthconnect/dto/AiConversationResponse$AiConversationResponseBuilder.class
com/healthconnect/service/ChatService.class
com/healthconnect/dto/AiChatRequest.class
com/healthconnect/controller/WebSocketController$TypingNotification.class
com/healthconnect/dto/AppointmentResponse$UserSummary.class
com/healthconnect/dto/SymptomAnalysisRequest.class
com/healthconnect/entity/Chat.class
com/healthconnect/repository/MessageRepository.class
com/healthconnect/entity/SymptomAnalysis.class
com/healthconnect/repository/AppointmentRepository.class
com/healthconnect/entity/Message.class
com/healthconnect/entity/AiConversation$AiConversationBuilder.class
com/healthconnect/dto/AiChatResponse$AiChatResponseBuilder.class
com/healthconnect/controller/ChatController.class
com/healthconnect/entity/SymptomAnalysis$SymptomAnalysisBuilder.class
com/healthconnect/repository/DoctorAvailabilityRepository.class
com/healthconnect/controller/AiHealthBotController.class
com/healthconnect/dto/AppointmentResponse.class
com/healthconnect/repository/SymptomAnalysisRepository.class
com/healthconnect/entity/AppointmentStatus.class
com/healthconnect/controller/UserController.class
com/healthconnect/entity/MessageStatus.class
com/healthconnect/controller/AuthController.class
com/healthconnect/service/UserService.class
com/healthconnect/entity/ChatType.class
com/healthconnect/config/SecurityConfig.class
com/healthconnect/entity/Appointment.class
